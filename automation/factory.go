package automation

import (
	"auto-register/browser/automation/action"
	"auto-register/browser/automation/step"
	"auto-register/generator"
)

// ActionFactory Action工厂，用于创建带有配置超时的Action
type ActionFactory struct {
	timeoutManager *TimeoutManager
}

// NewActionFactory 创建Action工厂
func NewActionFactory(timeoutManager *TimeoutManager) *ActionFactory {
	return &ActionFactory{
		timeoutManager: timeoutManager,
	}
}

// CreateInputAction 创建输入操作
func (f *ActionFactory) CreateInputAction(selector, value string) *action.InputElementAction {
	return f.timeoutManager.CreateInputActionWithTimeout(selector, value)
}

// CreateInputWithSelectorsAction 创建多选择器输入操作
func (f *ActionFactory) CreateInputWithSelectorsAction(selectors action.ElementSelectors, value string) *action.InputWithSelectorsAction {
	return f.timeoutManager.CreateInputWithSelectorsActionWithTimeout(selectors, value)
}

// CreateButtonAction 创建按钮操作
func (f *ActionFactory) CreateButtonAction(selector string) *action.ButtonElementAction {
	return f.timeoutManager.CreateButtonActionWithTimeout(selector)
}

// CreateButtonClickAction 创建按钮点击操作
func (f *ActionFactory) CreateButtonClickAction(selectors action.ElementSelectors) *action.ButtonClickAction {
	return f.timeoutManager.CreateButtonClickActionWithTimeout(selectors)
}

// CreateTurnstileAction 创建Turnstile操作
func (f *ActionFactory) CreateTurnstileAction() *action.TurnstileAction {
	return f.timeoutManager.CreateTurnstileActionWithTimeout()
}

// CreateNavigateAction 创建导航操作
func (f *ActionFactory) CreateNavigateAction(url string) *action.NavigateAction {
	return f.timeoutManager.CreateNavigateActionWithTimeout(url)
}

// CreateWaitForElementAction 创建等待元素操作
func (f *ActionFactory) CreateWaitForElementAction(selectors action.ElementSelectors) *action.WaitForElementAction {
	return f.timeoutManager.CreateWaitForElementActionWithTimeout(selectors)
}

// StepFactory Step工厂，用于创建带有配置超时的Step
type StepFactory struct {
	timeoutManager *TimeoutManager
}

// NewStepFactory 创建Step工厂
func NewStepFactory(timeoutManager *TimeoutManager) *StepFactory {
	return &StepFactory{
		timeoutManager: timeoutManager,
	}
}

// CreateFillEmailStep 创建填写邮箱步骤
func (f *StepFactory) CreateFillEmailStep(email string) *step.FillEmailStep {
	fillStep := step.NewFillEmailStep(email)
	// 这里可以应用超时配置，但FillEmailStep内部使用InputAction，
	// 所以超时会在Action层应用
	return fillStep
}

// CreateFillPasswordStep 创建填写密码步骤
func (f *StepFactory) CreateFillPasswordStep(password string) *step.FillPasswordStep {
	return step.NewFillPasswordStep(password)
}

// CreateFillFirstNameStep 创建填写名字步骤
func (f *StepFactory) CreateFillFirstNameStep(firstName string) *step.FillFirstNameStep {
	return step.NewFillFirstNameStep(firstName)
}

// CreateFillLastNameStep 创建填写姓氏步骤
func (f *StepFactory) CreateFillLastNameStep(lastName string) *step.FillLastNameStep {
	return step.NewFillLastNameStep(lastName)
}

// CreateClickContinueStep 创建点击继续步骤
func (f *StepFactory) CreateClickContinueStep() *step.ClickContinueStep {
	return step.NewClickContinueStep()
}

// CreateClickSubmitStep 创建点击提交步骤
func (f *StepFactory) CreateClickSubmitStep() *step.ClickSubmitStep {
	return step.NewClickSubmitStep()
}

// CreateClickLoginStep 创建点击登录步骤
func (f *StepFactory) CreateClickLoginStep() *step.ClickLoginStep {
	return step.NewClickLoginStep()
}

// CreateClickRegisterStep 创建点击注册步骤
func (f *StepFactory) CreateClickRegisterStep() *step.ClickRegisterStep {
	return step.NewClickRegisterStep()
}

// CreateNavigateToLoginPageStep 创建导航到登录页步骤
func (f *StepFactory) CreateNavigateToLoginPageStep(url string) *step.NavigateToLoginPageStep {
	return step.NewNavigateToLoginPageStep(url)
}

// CreateNavigateToRegisterPageStep 创建导航到注册页步骤
func (f *StepFactory) CreateNavigateToRegisterPageStep(url string) *step.NavigateToRegisterPageStep {
	return step.NewNavigateToRegisterPageStep(url)
}

// CreateWaitForPageLoadStep 创建等待页面加载步骤
func (f *StepFactory) CreateWaitForPageLoadStep() *step.WaitForPageLoadStep {
	return f.timeoutManager.CreateWaitForPageLoadStepWithTimeout()
}

// CreateHandleTurnstileStep 创建处理Turnstile步骤
func (f *StepFactory) CreateHandleTurnstileStep() *step.HandleTurnstileStep {
	return f.timeoutManager.CreateHandleTurnstileStepWithTimeout()
}

// CreateWaitForElementStep 创建等待元素步骤
func (f *StepFactory) CreateWaitForElementStep(selectors action.ElementSelectors, description string) *step.WaitForElementStep {
	return f.timeoutManager.CreateWaitForElementStepWithTimeout(selectors, description)
}

// CreateVerifyLoginResultStep 创建验证登录结果步骤
func (f *StepFactory) CreateVerifyLoginResultStep() *step.VerifyLoginResultStep {
	return step.NewVerifyLoginResultStep()
}

// FlowFactory Flow工厂，用于创建带有配置的Flow
type FlowFactory struct {
	timeoutManager *TimeoutManager
	actionFactory  *ActionFactory
	stepFactory    *StepFactory
}

// NewFlowFactory 创建Flow工厂
func NewFlowFactory(timeoutManager *TimeoutManager) *FlowFactory {
	return &FlowFactory{
		timeoutManager: timeoutManager,
		actionFactory:  NewActionFactory(timeoutManager),
		stepFactory:    NewStepFactory(timeoutManager),
	}
}

// GetActionFactory 获取Action工厂
func (f *FlowFactory) GetActionFactory() *ActionFactory {
	return f.actionFactory
}

// GetStepFactory 获取Step工厂
func (f *FlowFactory) GetStepFactory() *StepFactory {
	return f.stepFactory
}

// CreateStepSequenceForAccount 为账号创建步骤序列
func (f *FlowFactory) CreateStepSequenceForAccount(name string, account *generator.AccountInfo) *step.StepSequence {
	sequence := step.NewStepSequence(name)

	// 这里可以根据账号信息和超时配置创建标准的步骤序列
	// 例如：标准的注册流程、登录流程等

	return sequence
}

// CreateQuickRegistrationSequence 创建快速注册序列
func (f *FlowFactory) CreateQuickRegistrationSequence(account *generator.AccountInfo, baseURL string) *step.StepSequence {
	enableScreenshots := f.timeoutManager.IsScreenshotEnabled()

	// 创建导航步骤
	navStep := f.stepFactory.CreateNavigateToRegisterPageStep(baseURL)
	if enableScreenshots {
		navStep = navStep.WithScreenshot(true, "quick_register_page")
	}

	// 创建邮箱填写步骤
	emailStep := f.stepFactory.CreateFillEmailStep(account.Email)
	if enableScreenshots {
		emailStep = emailStep.WithScreenshot(true, "quick_email_filled")
	}

	// 创建点击提交步骤
	submitStep := f.stepFactory.CreateClickSubmitStep()
	if enableScreenshots {
		submitStep = submitStep.WithScreenshot(true, "quick_submit_clicked")
	}

	// 创建密码填写步骤
	passwordStep := f.stepFactory.CreateFillPasswordStep(account.Password)
	if enableScreenshots {
		passwordStep = passwordStep.WithScreenshot(true, "quick_password_filled")
	}

	// 创建注册按钮点击步骤
	registerStep := f.stepFactory.CreateClickRegisterStep()
	if enableScreenshots {
		registerStep = registerStep.WithScreenshot(true, "quick_register_clicked")
	}

	return step.NewStepSequence("QuickRegistration").
		Add(navStep).
		Add(emailStep).
		Add(submitStep).
		Add(passwordStep).
		Add(registerStep)
}

// CreateDebugRegistrationSequence 创建调试注册序列
func (f *FlowFactory) CreateDebugRegistrationSequence(account *generator.AccountInfo, baseURL string) *step.StepSequence {
	return step.NewStepSequence("DebugRegistration").
		Add(f.stepFactory.CreateNavigateToRegisterPageStep(baseURL).
			WithScreenshot(true, "debug_01_register_page")).
		Add(f.stepFactory.CreateWaitForPageLoadStep()).
		Add(f.stepFactory.CreateFillEmailStep(account.Email).
			WithScreenshot(true, "debug_02_email_filled")).
		Add(f.stepFactory.CreateClickSubmitStep().
			WithScreenshot(true, "debug_03_submit_clicked")).
		Add(f.stepFactory.CreateFillPasswordStep(account.Password).
			WithScreenshot(true, "debug_04_password_filled")).
		Add(f.stepFactory.CreateFillFirstNameStep(account.FirstName).
			WithScreenshot(true, "debug_05_firstname_filled")).
		Add(f.stepFactory.CreateFillLastNameStep(account.LastName).
			WithScreenshot(true, "debug_06_lastname_filled")).
		Add(f.stepFactory.CreateHandleTurnstileStep().WithRequired(false)).
		Add(f.stepFactory.CreateClickRegisterStep().
			WithScreenshot(true, "debug_07_register_clicked")).
		Add(f.stepFactory.CreateVerifyLoginResultStep())
}

// CreateStandardRegistrationSequence 创建标准注册序列
func (f *FlowFactory) CreateStandardRegistrationSequence(account *generator.AccountInfo, baseURL string) *step.StepSequence {
	enableScreenshots := f.timeoutManager.IsScreenshotEnabled()

	// 创建导航步骤
	navStep := f.stepFactory.CreateNavigateToRegisterPageStep(baseURL)
	if enableScreenshots {
		navStep = navStep.WithScreenshot(true, "register_page")
	}

	// 创建邮箱填写步骤
	emailStep := f.stepFactory.CreateFillEmailStep(account.Email)
	if enableScreenshots {
		emailStep = emailStep.WithScreenshot(true, "email_filled")
	}

	// 创建点击提交步骤
	submitStep := f.stepFactory.CreateClickSubmitStep()
	if enableScreenshots {
		submitStep = submitStep.WithScreenshot(true, "submit_clicked")
	}

	// 创建密码填写步骤
	passwordStep := f.stepFactory.CreateFillPasswordStep(account.Password)
	if enableScreenshots {
		passwordStep = passwordStep.WithScreenshot(true, "password_filled")
	}

	// 创建名字填写步骤
	firstNameStep := f.stepFactory.CreateFillFirstNameStep(account.FirstName)
	if enableScreenshots {
		firstNameStep = firstNameStep.WithScreenshot(true, "firstname_filled")
	}

	// 创建姓氏填写步骤
	lastNameStep := f.stepFactory.CreateFillLastNameStep(account.LastName)
	if enableScreenshots {
		lastNameStep = lastNameStep.WithScreenshot(true, "lastname_filled")
	}

	// 创建注册按钮点击步骤
	registerStep := f.stepFactory.CreateClickRegisterStep()
	if enableScreenshots {
		registerStep = registerStep.WithScreenshot(true, "register_clicked")
	}

	return step.NewStepSequence("StandardRegistration").
		Add(navStep).
		Add(emailStep).
		Add(submitStep).
		Add(passwordStep).
		Add(firstNameStep).
		Add(lastNameStep).
		Add(f.stepFactory.CreateHandleTurnstileStep().WithRequired(false)).
		Add(registerStep).
		Add(f.stepFactory.CreateVerifyLoginResultStep())
}

// CreateQuickLoginSequence 创建快速登录序列
func (f *FlowFactory) CreateQuickLoginSequence(email, password, baseURL string) *step.StepSequence {
	enableScreenshots := f.timeoutManager.IsScreenshotEnabled()

	// 创建导航步骤
	navStep := f.stepFactory.CreateNavigateToLoginPageStep(baseURL)
	if enableScreenshots {
		navStep = navStep.WithScreenshot(true, "quick_login_page")
	}

	// 创建邮箱填写步骤
	emailStep := f.stepFactory.CreateFillEmailStep(email)
	if enableScreenshots {
		emailStep = emailStep.WithScreenshot(true, "quick_email_filled")
	}

	// 创建继续按钮点击步骤
	continueStep := f.stepFactory.CreateClickContinueStep()
	if enableScreenshots {
		continueStep = continueStep.WithScreenshot(true, "quick_continue_clicked")
	}

	// 创建密码填写步骤
	passwordStep := f.stepFactory.CreateFillPasswordStep(password)
	if enableScreenshots {
		passwordStep = passwordStep.WithScreenshot(true, "quick_password_filled")
	}

	// 创建登录按钮点击步骤
	loginStep := f.stepFactory.CreateClickLoginStep()
	if enableScreenshots {
		loginStep = loginStep.WithScreenshot(true, "quick_login_clicked")
	}

	return step.NewStepSequence("QuickLogin").
		Add(navStep).
		Add(emailStep).
		Add(continueStep).
		Add(passwordStep).
		Add(loginStep)
}

// CreateDebugLoginSequence 创建调试登录序列
func (f *FlowFactory) CreateDebugLoginSequence(email, password, baseURL string) *step.StepSequence {
	return step.NewStepSequence("DebugLogin").
		Add(f.stepFactory.CreateNavigateToLoginPageStep(baseURL).
			WithScreenshot(true, "debug_01_login_page")).
		Add(f.stepFactory.CreateWaitForPageLoadStep()).
		Add(f.stepFactory.CreateFillEmailStep(email).
			WithScreenshot(true, "debug_02_email_filled")).
		Add(f.stepFactory.CreateClickContinueStep().
			WithScreenshot(true, "debug_03_continue_clicked")).
		Add(f.stepFactory.CreateFillPasswordStep(password).
			WithScreenshot(true, "debug_04_password_filled")).
		Add(f.stepFactory.CreateHandleTurnstileStep().WithRequired(false)).
		Add(f.stepFactory.CreateClickLoginStep().
			WithScreenshot(true, "debug_05_login_clicked")).
		Add(f.stepFactory.CreateVerifyLoginResultStep())
}

// CreateStandardLoginSequence 创建标准登录序列
func (f *FlowFactory) CreateStandardLoginSequence(email, password, baseURL string) *step.StepSequence {
	enableScreenshots := f.timeoutManager.IsScreenshotEnabled()

	// 创建导航步骤
	navStep := f.stepFactory.CreateNavigateToLoginPageStep(baseURL)
	if enableScreenshots {
		navStep = navStep.WithScreenshot(true, "login_page")
	}

	// 创建邮箱填写步骤
	emailStep := f.stepFactory.CreateFillEmailStep(email)
	if enableScreenshots {
		emailStep = emailStep.WithScreenshot(true, "login_email_filled")
	}

	// 创建继续按钮点击步骤
	continueStep := f.stepFactory.CreateClickContinueStep()
	if enableScreenshots {
		continueStep = continueStep.WithScreenshot(true, "continue_clicked")
	}

	// 创建密码填写步骤
	passwordStep := f.stepFactory.CreateFillPasswordStep(password)
	if enableScreenshots {
		passwordStep = passwordStep.WithScreenshot(true, "login_password_filled")
	}

	// 创建登录按钮点击步骤
	loginStep := f.stepFactory.CreateClickLoginStep()
	if enableScreenshots {
		loginStep = loginStep.WithScreenshot(true, "login_clicked")
	}

	return step.NewStepSequence("StandardLogin").
		Add(navStep).
		Add(emailStep).
		Add(continueStep).
		Add(passwordStep).
		Add(f.stepFactory.CreateHandleTurnstileStep().WithRequired(false)).
		Add(loginStep).
		Add(f.stepFactory.CreateVerifyLoginResultStep())
}
