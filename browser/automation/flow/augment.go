package flow

import (
	"fmt"

	"auto-register/browser/automation/action"
	"auto-register/browser/automation/step"
	"auto-register/generator"
	"auto-register/logger"
)

// AugmentLoginFlow Augment登录流程
type AugmentLoginFlow struct {
	BaseURL string
}

// NewAugmentLoginFlow 创建Augment登录流程
func NewAugmentLoginFlow() *AugmentLoginFlow {
	return &AugmentLoginFlow{
		BaseURL: "https://login.augmentcode.com/",
	}
}

// WithBaseURL 设置基础URL
func (f *AugmentLoginFlow) WithBaseURL(url string) *AugmentLoginFlow {
	f.BaseURL = url
	return f
}

// LoginWithCredentials 使用凭据登录
func (f *AugmentLoginFlow) LoginWithCredentials(ctx *action.ActionContext, email, password string) error {
	logger.Info("开始Augment登录流程")

	// 创建步骤序列
	sequence := step.NewStepSequence("AugmentLogin").
		Add(step.NewNavigateToLoginPageStep(f.BaseURL).
			WithScreenshot(true, "augment_login_page")).
		Add(step.NewWaitForPageLoadStep()).
		Add(step.NewFillEmailStep(email).
			WithScreenshot(true, "augment_email_filled")).
		Add(step.NewClickContinueStep()).
		Add(step.NewWaitForPageLoadStep()).
		Add(step.NewFillPasswordStep(password).
			WithScreenshot(true, "augment_password_filled")).
		Add(step.NewClickLoginStep()).
		Add(step.NewHandleTurnstileStep().WithRequired(false)).
		Add(step.NewVerifyLoginResultStep().
			WithSuccessURLs([]string{"augmentcode.com"}).
			WithAllowUnknownStatus(true))

	if err := sequence.Execute(ctx); err != nil {
		return fmt.Errorf("Augment登录流程失败: %w", err)
	}

	logger.Info("Augment登录流程完成")
	return nil
}

// LoginWithAccount 使用账号信息登录
func (f *AugmentLoginFlow) LoginWithAccount(ctx *action.ActionContext, account *generator.AccountInfo) error {
	return f.LoginWithCredentials(ctx, account.Email, account.Password)
}

// QuickLogin 快速登录（最小步骤）
func (f *AugmentLoginFlow) QuickLogin(ctx *action.ActionContext, email, password string) error {
	logger.Info("开始Augment快速登录")

	sequence := step.NewStepSequence("AugmentQuickLogin").
		Add(step.NewNavigateToLoginPageStep(f.BaseURL)).
		Add(step.NewFillEmailStep(email)).
		Add(step.NewClickContinueStep()).
		Add(step.NewFillPasswordStep(password)).
		Add(step.NewClickLoginStep()).
		Add(step.NewVerifyLoginResultStep())

	if err := sequence.Execute(ctx); err != nil {
		return fmt.Errorf("Augment快速登录失败: %w", err)
	}

	logger.Info("Augment快速登录完成")
	return nil
}

// DebugLogin 调试登录（包含详细调试信息）
func (f *AugmentLoginFlow) DebugLogin(ctx *action.ActionContext, email, password string) error {
	logger.Info("开始Augment调试登录")

	sequence := step.NewStepSequence("AugmentDebugLogin").
		Add(step.NewNavigateToLoginPageStep(f.BaseURL).
			WithScreenshot(true, "debug_01_login_page")).
		Add(step.NewWaitForPageLoadStep()).
		Add(step.NewFillEmailStep(email).
			WithScreenshot(true, "debug_03_email_filled")).
		Add(step.NewClickContinueStep().
			WithScreenshot(true, "debug_04_continue_clicked")).
		Add(step.NewFillPasswordStep(password).
			WithScreenshot(true, "debug_05_password_filled")).
		Add(step.NewClickLoginStep().
			WithScreenshot(true, "debug_06_login_clicked")).
		Add(step.NewHandleTurnstileStep().WithRequired(false)).
		Add(step.NewVerifyLoginResultStep())

	if err := sequence.Execute(ctx); err != nil {
		return fmt.Errorf("Augment调试登录失败: %w", err)
	}

	logger.Info("Augment调试登录完成")
	return nil
}

// GetName 获取流程名称
func (f *AugmentLoginFlow) GetName() string {
	return "AugmentLogin"
}
