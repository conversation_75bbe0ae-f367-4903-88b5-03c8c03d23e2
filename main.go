package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	"auto-register/auth"
	"auto-register/browser"
	"auto-register/browser/automation/action"
	"auto-register/browser/automation/flow"
	"auto-register/config"
	"auto-register/email"
	"auto-register/generator"
	"auto-register/logger"
)

const (
	AppName    = "Auto Register Framework"
	AppVersion = "v1.0.0"
)

func main() {
	printLogo()

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("配置加载失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.InitLogger(cfg.LogLevel, cfg.LogFile); err != nil {
		fmt.Printf("日志初始化失败: %v\n", err)
		os.Exit(1)
	}

	logger.Info("=== Auto Register Framework 启动 ===")
	cfg.PrintConfig()

	// 选择操作模式
	mode := selectOperationMode()

	switch mode {
	case 1:
		// 仅重置机器码
		logger.Info("执行机器码重置...")
		if err := resetMachineID(); err != nil {
			logger.Errorf("机器码重置失败: %v", err)
			os.Exit(1)
		}
		logger.Info("机器码重置完成")

	case 2:
		// 完整注册流程
		if err := runFullRegistration(cfg); err != nil {
			logger.Errorf("注册流程失败: %v", err)
			os.Exit(1)
		}

	default:
		logger.Error("无效的操作模式")
		os.Exit(1)
	}

	logger.Info("=== 程序执行完成 ===")
	fmt.Println("\n按回车键退出...")
	fmt.Scanln()
}

// printLogo 打印程序Logo
func printLogo() {
	fmt.Println(`
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║     █████╗ ██╗   ██╗████████╗ ██████╗       ██████╗ ███████╗ ║
║    ██╔══██╗██║   ██║╚══██╔══╝██╔═══██╗      ██╔══██╗██╔════╝ ║
║    ███████║██║   ██║   ██║   ██║   ██║█████╗██████╔╝█████╗   ║
║    ██╔══██║██║   ██║   ██║   ██║   ██║╚════╝██╔══██╗██╔══╝   ║
║    ██║  ██║╚██████╔╝   ██║   ╚██████╔╝      ██║  ██║███████╗ ║
║    ╚═╝  ╚═╝ ╚═════╝    ╚═╝    ╚═════╝       ╚═╝  ╚═╝╚══════╝ ║
║                                                              ║
║                   Auto Register Framework                    ║
║                        ` + AppVersion + `                   		║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`)
}

// selectOperationMode 选择操作模式
func selectOperationMode() int {
	fmt.Println("\n请选择操作模式:")
	fmt.Println("1. 仅重置机器码")
	fmt.Println("2. 完整注册流程")

	for {
		fmt.Print("\n请输入选项 (1-2): ")
		var choice int
		if _, err := fmt.Scanf("%d", &choice); err != nil {
			fmt.Println("请输入有效的数字")
			continue
		}

		if choice >= 1 && choice <= 2 {
			return choice
		}

		fmt.Println("请输入有效的选项 (1-2)")
	}
}

// runFullRegistration 运行完整注册流程
func runFullRegistration(cfg *config.Config) error {
	logger.Info("开始完整注册流程")

	// 生成账号信息
	accountGen := generator.NewAccountGenerator(cfg.Domain)
	account := accountGen.GenerateAccount()

	logger.Infof("生成账号信息:")
	logger.Infof("  邮箱: %s", account.Email)
	logger.Infof("  密码: %s", account.Password)
	logger.Infof("  姓名: %s %s", account.FirstName, account.LastName)

	// 初始化浏览器管理器
	browserManager := browser.NewManager(cfg)
	if err := browserManager.Start(); err != nil {
		return fmt.Errorf("启动浏览器失败: %w", err)
	}
	defer browserManager.Stop()

	// 创建操作上下文
	ctx := action.NewActionContext(browserManager)

	// 初始化邮箱验证处理器
	emailHandler := email.NewVerificationHandler(cfg, account.Email)

	// 执行自动化流程
	logger.Info("开始执行自动化流程...")

	// 根据配置选择流程
	var err error
	switch cfg.AutomationFlow {
	case "cursor_register":
		err = executeCursorRegisterFlow(ctx, cfg, account)
	case "augment_login":
		err = executeAugmentLoginFlow(ctx, cfg, account)
	default:
		err = fmt.Errorf("不支持的自动化流程: %s", cfg.AutomationFlow)
	}

	// 执行流程
	if err != nil {
		// 如果需要邮箱验证，则处理邮箱验证
		if strings.Contains(err.Error(), "需要邮箱验证码") {
			logger.Info("开始处理邮箱验证...")

			// 获取验证码
			verificationTimeout := time.Duration(cfg.VerificationTimeout) * time.Second
			code, err := emailHandler.GetVerificationCodeWithTimeout(verificationTimeout)
			if err != nil {
				return fmt.Errorf("获取验证码失败: %w", err)
			}

			logger.Infof("获取到验证码: %s", code)

			// 输入验证码
			if err := inputVerificationCode(browserManager, code); err != nil {
				return fmt.Errorf("输入验证码失败: %w", err)
			}

			logger.Info("邮箱验证完成")
		} else {
			return fmt.Errorf("自动化流程失败: %w", err)
		}
	}

	// 生成模拟令牌（用于演示）
	token := fmt.Sprintf("mock_token_%d", time.Now().Unix())

	// 更新Cursor认证信息
	authManager, err := auth.NewCursorAuthManager()
	if err != nil {
		return fmt.Errorf("创建认证管理器失败: %w", err)
	}

	if err := authManager.UpdateAuth(account.Email, token, token); err != nil {
		return fmt.Errorf("更新认证信息失败: %w", err)
	}

	logger.Info("注册流程完成!")
	logger.Infof("账号信息: %s / %s", account.Email, account.Password)

	return nil
}

// inputVerificationCode 输入验证码
func inputVerificationCode(browserManager *browser.Manager, code string) error {
	logger.Info("输入验证码")

	// 等待验证码输入框出现
	if err := browserManager.WaitForElement(`input[data-index="0"]`, 10*time.Second); err != nil {
		return fmt.Errorf("等待验证码输入框失败: %w", err)
	}

	// 逐个输入验证码数字
	for i, digit := range code {
		selector := fmt.Sprintf(`input[data-index="%d"]`, i)
		if err := browserManager.SendKeys(selector, string(digit)); err != nil {
			return fmt.Errorf("输入验证码第%d位失败: %w", i+1, err)
		}

		// 短暂延迟，模拟人类输入
		time.Sleep(100 * time.Millisecond)
	}

	logger.Info("验证码输入完成")
	return nil
}

// executeCursorRegisterFlow 执行Cursor注册流程
func executeCursorRegisterFlow(ctx *action.ActionContext, cfg *config.Config, account *generator.AccountInfo) error {
	logger.Info("执行Cursor注册流程")

	// 创建Cursor注册流程
	cursorFlow := flow.NewCursorRegisterFlow().WithBaseURL(cfg.FlowConfig.CursorBaseURL)

	// 根据配置选择执行模式
	if cfg.FlowConfig.CursorQuickMode {
		logger.Info("使用快速注册模式")
		return cursorFlow.QuickRegister(ctx, account)
	} else if cfg.FlowConfig.CursorDebugMode {
		logger.Info("使用调试注册模式")
		return cursorFlow.DebugRegister(ctx, account)
	} else {
		logger.Info("使用标准注册模式")
		return cursorFlow.RegisterWithAccount(ctx, account)
	}
}

// executeAugmentLoginFlow 执行Augment登录流程
func executeAugmentLoginFlow(ctx *action.ActionContext, cfg *config.Config, account *generator.AccountInfo) error {
	logger.Info("执行Augment登录流程")

	// 创建Augment登录流程
	augmentFlow := flow.NewAugmentLoginFlow().WithBaseURL(cfg.FlowConfig.AugmentBaseURL)

	// 根据配置选择执行模式
	if cfg.FlowConfig.AugmentQuickMode {
		logger.Info("使用快速登录模式")
		return augmentFlow.QuickLogin(ctx, account.Email, account.Password)
	} else if cfg.FlowConfig.AugmentDebugMode {
		logger.Info("使用调试登录模式")
		return augmentFlow.DebugLogin(ctx, account.Email, account.Password)
	} else {
		logger.Info("使用标准登录模式")
		return augmentFlow.LoginWithAccount(ctx, account)
	}
}

// resetMachineID 重置机器码（占位符实现）
func resetMachineID() error {
	logger.Info("开始重置机器码...")

	// 这里应该实现实际的机器码重置逻辑
	// 可以参考 go-cursor-help 项目的实现
	// 或者调用外部工具

	// 模拟重置过程
	time.Sleep(2 * time.Second)

	logger.Info("机器码重置逻辑尚未实现")
	logger.Info("请参考以下项目实现机器码重置:")
	logger.Info("- https://github.com/yuaotian/go-cursor-help")
	logger.Info("- https://github.com/chengazhen/cursor-auto-free")

	return nil
}
